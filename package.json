{"name": "my-ts-express-app", "version": "1.0.0", "description": "A TypeScript Express.js application", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "postbuild": "echo 'Build completed successfully!'", "start:prod": "npm run build && npm start", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts"}, "keywords": ["typescript", "express", "nodejs", "api"], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.33.0", "@types/express": "^4.17.21", "@types/node": "^20.14.9", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^9.33.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.5.3"}, "dependencies": {"dotenv": "^17.2.1", "express": "^4.21.2", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.25"}}