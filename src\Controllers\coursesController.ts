import { Request, Response } from "express";
import CourseService from "../Services/course.service";
import { sendResponse } from "../utils/sendResponse";
class CourseController {
  async getAll(_req: Request, res: Response) {
    try {
      const courses = await CourseService.getAllCourses();
      sendResponse(res, 200, "Courses retrieved successfully", courses);
    } catch (error) {
      sendResponse(res, 500, "Failed to get courses", error);
    }
  }

  async getById(req: Request, res: Response) {
    try {
      const id = Number(req.params.id);
      const course = await CourseService.getCourseById(id);
      if (!course) {
        return sendResponse(res, 404, "Course not found", null);
      }
      sendResponse(res, 200, "Course retrieved successfully", course);
    } catch (error) {
      sendResponse(res, 500, "Failed to get course", error);
    }
  }

  async create(req: Request, res: Response) {
    try {
      const courseData = req.body;
      const createdCourse = await CourseService.createCourse(courseData);
      sendResponse(res, 201, "Course created successfully", createdCourse);
    } catch (error) {
      sendResponse(res, 500, "Failed to create course", error);
    }
  }

  async update(req: Request, res: Response) {
    try {
      const id = Number(req.params.id);
      const courseData = req.body;
      const updatedCourse = await CourseService.updateCourse(id, courseData);
      if (!updatedCourse) {
        return sendResponse(res, 404, "Course not found", null);
      }
      sendResponse(res, 200, "Course updated successfully", updatedCourse);
    } catch (error) {
      sendResponse(res, 500, "Failed to update course", error);
    }
  }

  async delete(req: Request, res: Response) {
    try {
      const id = Number(req.params.id);
      const result = await CourseService.deleteCourse(id);
      if (result.affected === 0) {
        return sendResponse(res, 404, "Course not found", null);
      }
      res.status(204).send();
    } catch (error) {
      sendResponse(res, 500, "Failed to delete course", error);
    }
  }
}

export default new CourseController();
