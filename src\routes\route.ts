import express from 'express';
import { Request, Response } from 'express'; 
import CourseController from '../Controllers/coursesController' ; 

export const route = express.Router(); 

route.get('/', (_req : Request, res  : Response ) => {
    res.send('Hello World!');
});


route.get('/getallCourses' , CourseController.getAll); 
route.post('/addCourse' , CourseController.create); 
route.put('/updateCourse/:id' , CourseController.update); 
route.delete('/deleteCourse/:id' , CourseController.delete);  