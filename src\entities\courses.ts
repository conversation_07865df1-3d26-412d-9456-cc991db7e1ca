import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from "typeorm";

@Entity()
export class Course {
  @PrimaryGeneratedColumn()
  id!: number;    

  @Column({ length: 100 })
  title!: string;

  @Column("text")
  description!: string;

  @Column({ length: 50 })
  category!: string;

  @Column("decimal", { precision: 10, scale: 2 })
  price!: number;

  @Column({ default: true })
  isActive!: boolean;
 

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}