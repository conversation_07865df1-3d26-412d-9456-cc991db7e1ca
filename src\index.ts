// src/index.ts
import express from 'express';
import {route} from './routes/route'; 
import { AppDataSource } from './config/db';
const app = express();
const port = 3011;

app.use('/api' , route ) ; 

AppDataSource.initialize()
  .then(() => {
    console.log("Database connected successfully!");

    app.listen(port, () => {
      console.log(`Server running at http://localhost:${port}`);
    });
  })
  .catch((error) => {
    console.error("Database connection failed:", error);
    process.exit(1);  
  });

