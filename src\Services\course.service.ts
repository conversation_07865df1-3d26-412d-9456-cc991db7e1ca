import { AppDataSource } from "../config/db";
import { Course } from "../entities/courses";

class CourseService {
  private courseRepository = AppDataSource.getRepository(Course);

  async getAllCourses() {
    return this.courseRepository.find();
  }

  async getCourseById(id: number) {
    return this.courseRepository.findOneBy({ id });
  }

async createCourse(courseData: Partial<Course>): Promise<Course> {
  const course = this.courseRepository.create(courseData);
  return await this.courseRepository.save(course);
}


  async updateCourse(id: number, course: Partial<Course>) {
    await this.courseRepository.update(id, course);
    return this.getCourseById(id);
  }

  async deleteCourse(id: number) {
    return this.courseRepository.delete(id);
  }
}

export default new CourseService();
